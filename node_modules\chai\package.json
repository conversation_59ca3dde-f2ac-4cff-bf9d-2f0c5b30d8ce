{"author": "<PERSON> <<EMAIL>>", "name": "chai", "type": "module", "description": "BDD/TDD assertion library for node.js and the browser. Test framework agnostic.", "keywords": ["test", "assertion", "assert", "testing", "chai"], "homepage": "http://chaijs.com", "license": "MIT", "contributors": ["<PERSON> <<EMAIL>>", "Domenic Denicola <<EMAIL>> (http://domenicdenicola.com)", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "version": "5.2.0", "repository": {"type": "git", "url": "https://github.com/chaijs/chai"}, "bugs": {"url": "https://github.com/chaijs/chai/issues"}, "main": "./chai.js", "scripts": {"prebuild": "npm run clean", "build": "npm run build:esm", "build:esm": "esbuild --bundle --format=esm --keep-names --outfile=chai.js index.js", "format": "prettier --write lib", "pretest": "npm run lint && npm run build", "test": "npm run test-node && npm run test-chrome", "test-node": "c8 --99 --check-coverage mocha --require ./test/bootstrap/index.js test/*.js", "test-chrome": "web-test-runner --playwright", "lint": "npm run lint:js && npm run lint:format", "lint:js": "eslint lib/", "lint:format": "prettier --check lib", "clean": "rm -rf chai.js coverage/"}, "engines": {"node": ">=12"}, "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@rollup/plugin-commonjs": "^25.0.7", "@web/dev-server-rollup": "^0.6.1", "@web/test-runner": "^0.18.0", "@web/test-runner-playwright": "^0.11.0", "c8": "^10.1.3", "esbuild": "^0.19.10", "eslint": "^8.56.0", "eslint-plugin-jsdoc": "^48.0.4", "mocha": "^10.2.0", "prettier": "^3.4.2"}}