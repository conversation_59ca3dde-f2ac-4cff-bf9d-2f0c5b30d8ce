{"name": "@types/marked-terminal", "version": "6.1.1", "description": "TypeScript definitions for marked-terminal", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/marked-terminal", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "b<PERSON><PERSON><PERSON>", "url": "https://github.com/bkendall"}], "type": "module", "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.ts", "default": "./index.d.cts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/marked-terminal"}, "scripts": {}, "dependencies": {"@types/cardinal": "^2.1", "@types/node": "*", "chalk": "^5.3.0", "marked": ">=6.0.0 <12"}, "typesPublisherContentHash": "a228eb1ece6e64f1f36f4c0f5523671a0d0aefa2a2bc6ad2dcc58400a2c3bc78", "typeScriptVersion": "4.6"}