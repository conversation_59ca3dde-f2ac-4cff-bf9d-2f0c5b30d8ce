{"name": "cli-spinners", "version": "3.2.0", "description": "Spinners for use in the terminal", "license": "MIT", "repository": "sindresorhus/cli-spinners", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18.20"}, "scripts": {"//test": "xo && ava && tsc --noEmit index.d.ts", "test": "ava && tsc --noEmit index.d.ts", "asciicast": "asciinema rec --command='node example-all.js' --title='cli-spinner' --quiet"}, "files": ["index.js", "index.d.ts", "spinners.json"], "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle", "json"], "devDependencies": {"ava": "^6.1.2", "log-update": "^6.0.0", "string-length": "^6.0.0", "typescript": "^5.4.5", "xo": "^0.58.0"}}