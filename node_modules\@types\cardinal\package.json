{"name": "@types/cardinal", "version": "2.1.1", "description": "TypeScript definitions for cardinal", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cardinal", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "and<PERSON><PERSON><PERSON>", "url": "https://github.com/andrewbranch"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cardinal"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "103589381c9909e6b2027e90ca44d644b204546b0aa473fba3dcf08cdc1e1fa2", "typeScriptVersion": "4.5"}