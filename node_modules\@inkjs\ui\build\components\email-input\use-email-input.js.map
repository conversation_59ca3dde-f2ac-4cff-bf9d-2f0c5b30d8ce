{"version": 3, "file": "use-email-input.js", "sourceRoot": "", "sources": ["../../../source/components/email-input/use-email-input.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,OAAO,EAAC,MAAM,OAAO,CAAC;AAC9B,OAAO,EAAC,QAAQ,EAAC,MAAM,KAAK,CAAC;AAC7B,OAAO,KAAK,MAAM,OAAO,CAAC;AA+B1B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAElC,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,EAC7B,UAAU,GAAG,KAAK,EAClB,KAAK,EACL,WAAW,GAAG,EAAE,GACI,EAAsB,EAAE;IAC5C,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,EAAE;QACxC,IAAI,UAAU,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,CAAC;QAED,OAAO,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC;YAC3C,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,MAAM,CAAC;IACX,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAE9B,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,EAAE;QAClC,IAAI,UAAU,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC,KAAK,CAAC;QACpB,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;QAElD,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEpE,KAAK,EAAE,CAAC;QACT,CAAC;QAED,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC/C,MAAM;oBACL,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;wBAClC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACP,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACvC,CAAC;YAED,OAAO,MAAM,CAAC;QACf,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACzE,MAAM,IAAI,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,MAAM,CAAC;IACf,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;IAEpE,QAAQ,CACP,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACd,IACC,GAAG,CAAC,OAAO;YACX,GAAG,CAAC,SAAS;YACb,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,CAAC;YAC3B,GAAG,CAAC,GAAG;YACP,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,EACrB,CAAC;YACF,OAAO;QACR,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,KAAK,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACR,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YACnB,KAAK,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YAC3B,KAAK,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;aAAM,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACxC,KAAK,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;aAAM,CAAC;YACP,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACF,CAAC,EACD,EAAC,QAAQ,EAAE,CAAC,UAAU,EAAC,CACvB,CAAC;IAEF,OAAO;QACN,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,mBAAmB;KACxE,CAAC;AACH,CAAC,CAAC"}